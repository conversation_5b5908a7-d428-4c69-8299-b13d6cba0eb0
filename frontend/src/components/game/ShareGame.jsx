import { useState } from 'react';
import PropTypes from 'prop-types';
import { FaFacebook, FaTwitter, FaWhatsapp, FaLinkedin, FaReddit, FaTimes, FaCopy, FaShare } from 'react-icons/fa';

/**
 * Component for sharing games on social media platforms
 */
const ShareGame = ({ gameTitle, gameUrl }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [copied, setCopied] = useState(false);

  const shareUrl = gameUrl || window.location.href;
  const shareText = `Check out this awesome game: ${gameTitle}`;

  const shareLinks = [
    {
      name: 'Facebook',
      icon: FaFacebook,
      color: 'bg-blue-600 hover:bg-blue-700',
      url: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`
    },
    {
      name: 'Twitter',
      icon: FaTwitter,
      color: 'bg-black hover:bg-gray-800',
      url: `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`
    },
    {
      name: 'WhatsApp',
      icon: FaWhatsapp,
      color: 'bg-green-500 hover:bg-green-600',
      url: `https://wa.me/?text=${encodeURIComponent(`${shareText} ${shareUrl}`)}`
    },
    {
      name: 'LinkedIn',
      icon: FaLinkedin,
      color: 'bg-blue-700 hover:bg-blue-800',
      url: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`
    },
    {
      name: 'Reddit',
      icon: FaReddit,
      color: 'bg-orange-600 hover:bg-orange-700',
      url: `https://reddit.com/submit?url=${encodeURIComponent(shareUrl)}&title=${encodeURIComponent(shareText)}`
    }
  ];

  const handleShare = (url) => {
    window.open(url, '_blank', 'width=600,height=400');
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy link:', err);
    }
  };

  return (
    <>
      {/* Share Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="inline-flex items-center gap-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 transform hover:scale-105"
      >
        <FaShare className="text-sm" />
        Share this game
      </button>

      {/* Share Modal */}
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-2xl p-6 w-full max-w-md relative">
            {/* Close Button */}
            <button
              onClick={() => setIsOpen(false)}
              className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
            >
              <FaTimes size={20} />
            </button>

            {/* Modal Header */}
            <h3 className="text-xl font-bold text-white mb-6 text-center">
              Share this game
            </h3>

            {/* Social Media Icons */}
            <div className="flex justify-center gap-3 mb-6">
              {shareLinks.map((platform) => {
                const IconComponent = platform.icon;
                return (
                  <button
                    key={platform.name}
                    onClick={() => handleShare(platform.url)}
                    className={`${platform.color} p-3 rounded-full text-white transition-all duration-200 transform hover:scale-110`}
                    title={`Share on ${platform.name}`}
                  >
                    <IconComponent size={20} />
                  </button>
                );
              })}
            </div>

            {/* Copy Link Section */}
            <div className="bg-gray-700 rounded-lg p-3 flex items-center gap-3">
              <input
                type="text"
                value={shareUrl}
                readOnly
                className="flex-1 bg-transparent text-gray-300 text-sm outline-none"
              />
              <button
                onClick={handleCopyLink}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                  copied
                    ? 'bg-green-600 text-white'
                    : 'bg-purple-600 hover:bg-purple-700 text-white'
                }`}
              >
                {copied ? (
                  <>
                    <FaCopy className="inline mr-1" />
                    Copied!
                  </>
                ) : (
                  <>
                    <FaCopy className="inline mr-1" />
                    Copy
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

ShareGame.propTypes = {
  gameTitle: PropTypes.string.isRequired,
  gameUrl: PropTypes.string
};

export default ShareGame;
