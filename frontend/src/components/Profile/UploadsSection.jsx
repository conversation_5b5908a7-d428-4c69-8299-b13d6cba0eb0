import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { FaUpload, FaEdit, FaShoppingCart } from 'react-icons/fa';
import { API_URL } from '../../config/env.js';
import { gamePlaceholder } from '../../assets/placeholders.js';
import { getSecureImageUrl } from '../../utils/imageUtils';
import { useLanguageNavigation } from '../../hooks/useLanguageNavigation';

// Helper function to format dates
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';

  const date = new Date(dateString);

  // Check if the date is valid
  if (isNaN(date.getTime())) {
    console.warn('Invalid date string:', dateString);
    return 'Invalid Date';
  }

  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};

const UploadsSection = () => {
  const { createLanguageLink } = useLanguageNavigation();
  const [myUploads, setMyUploads] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [analytics, setAnalytics] = useState({
    totalDownloads: 0,
    totalAdImpressions: 0,
    totalAdRevenue: 0,
    averageRating: 0
  });

  // Fetch user's uploaded games when component mounts
  useEffect(() => {
    const fetchUserUploads = async () => {
      try {
        setLoading(true);
        // Use the absolute URL to the backend API
        const response = await fetch(`${API_URL}/games/my-uploads`, {
          credentials: 'include', // Use cookies for authentication
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to fetch your uploaded games');
        }

        const data = await response.json();
        console.log('Uploads API response:', data);

        // Transform API data to match the component's expected format
        const formattedUploads = data.games.map(game => ({
          id: game.id,
          title: game.title,
          description: game.description,
          image: game.cardImage || game.coverImage || gamePlaceholder,
          downloads: game.downloadCount || 0,
          rating: game.averageRating || 0,
          status: game.status || 'pending',
          date: game.createdAt,
          revenue: game.adRevenue || '0.00',
          adImpressions: game.adImpressions || 0,
          adRevenue: game.adRevenue || '0.00',
          genre: game.genre,
          priceModel: game.priceModel
        }));

        setMyUploads(formattedUploads);

        // Calculate analytics totals from API response
        const totalDownloads = data.totalPlays || 0;
        const totalAdImpressions = data.totalAdImpressions || 0;
        const totalAdRevenue = parseFloat(data.totalAdRevenue) || 0;
        const ratings = data.games.filter(game => game.averageRating > 0).map(game => game.averageRating);
        const averageRating = ratings.length > 0
          ? (ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length).toFixed(1)
          : 0;

        setAnalytics({
          totalDownloads,
          totalAdImpressions,
          totalAdRevenue,
          averageRating
        });
        
        setLoading(false);
      } catch (err) {
        console.error('Error fetching user uploads:', err);
        setError(err.message);
        setLoading(false);
      }
    };

    fetchUserUploads();
  }, []);

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center py-16">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mb-4"></div>
        <p className="text-gray-400">Loading your uploads...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-16 text-center">
        <h3 className="text-xl font-semibold text-white mb-2">Error loading uploads</h3>
        <p className="text-gray-400 mb-4">{error}</p>
        <button 
          onClick={() => window.location.reload()} 
          className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
        >
          Try Again
        </button>
      </div>
    );
  }
  
  return (
    <section className="space-y-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <h1 className="text-3xl font-bold text-white">My Uploaded Games</h1>
        <Link
          to={createLanguageLink("/upload-game")}
          className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2 no-underline w-fit"
        >
          <FaUpload /> Upload New Game
        </Link>
      </div>
      
      {myUploads.length > 0 ? (
        <div className="space-y-6">
          {myUploads.map(game => (
            <div key={game.id} className="bg-gray-700/30 p-6 rounded-lg border border-gray-600 flex flex-col lg:flex-row gap-6">
              <div className="relative lg:w-48 lg:h-32">
                <img 
                  src={getSecureImageUrl(game.image)} 
                  alt={game.title} 
                  className="w-full h-32 lg:h-full object-cover rounded-lg"
                  onError={(e) => {
                    e.target.onerror = null; 
                    e.target.src = gamePlaceholder;
                  }}
                />
                <span className={`absolute top-2 right-2 px-2 py-1 text-xs font-medium rounded-full ${
                  game.status === 'approved' 
                    ? 'bg-green-500/20 text-green-300' 
                    : 'bg-yellow-500/20 text-yellow-300'
                }`}>
                  {game.status === 'approved' ? 'Live' : 'Pending Review'}
                </span>
              </div>
              
              <div className="flex-1 space-y-4">
                <h3 className="text-xl font-semibold text-white">{game.title}</h3>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                  <div className="text-center">
                    <span className="block text-sm text-gray-400">Plays</span>
                    <span className="block text-lg font-semibold text-white">{game.downloads}</span>
                  </div>

                  <div className="text-center">
                    <span className="block text-sm text-gray-400">Rating</span>
                    <span className="block text-lg font-semibold text-white">
                      {game.rating != null && !isNaN(Number(game.rating)) && Number(game.rating) > 0
                        ? Number(game.rating).toFixed(1) + ' ★'
                        : 'No ratings'}
                    </span>
                  </div>

                  <div className="text-center">
                    <span className="block text-sm text-gray-400">Ad Impressions</span>
                    <span className="block text-lg font-semibold text-white">{game.adImpressions}</span>
                  </div>

                  <div className="text-center">
                    <span className="block text-sm text-gray-400">Ad Revenue</span>
                    <span className="block text-lg font-semibold text-green-400">${game.adRevenue}</span>
                  </div>

                  <div className="text-center">
                    <span className="block text-sm text-gray-400">Uploaded</span>
                    <span className="block text-lg font-semibold text-white">{formatDate(game.date)}</span>
                  </div>
                </div>
              </div>
              
              <div className="flex flex-col gap-2 lg:w-32">
                <Link
                  to={createLanguageLink(`/edit-game/${game.id}`)}
                  className="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2 no-underline"
                >
                  <FaEdit /> Edit
                </Link>
                <Link
                  to={createLanguageLink(`/game/${game.id}`)}
                  className="border border-gray-500 hover:border-gray-400 text-gray-300 hover:text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 text-center no-underline"
                >
                  View Game
                </Link>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center py-16 text-center">
          <FaShoppingCart className="text-6xl text-gray-600 mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">No uploaded games</h3>
          <p className="text-gray-400 mb-6">Share your games with the community</p>
          <Link
            to={createLanguageLink("/upload-game")}
            className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 no-underline"
          >
            Upload Game
          </Link>
        </div>
      )}
      
      {myUploads.length > 0 && (
        <div className="space-y-6">
          <h2 className="text-2xl font-bold text-white">Your Analytics Summary</h2>
          <div className="bg-yellow-500/10 border border-yellow-500/30 p-4 rounded-lg mb-6">
            <p className="text-yellow-400 text-sm">
              <strong>Note:</strong> Ads are currently a work in progress and will be added soon. Revenue calculations are estimates based on projected ad performance.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-gray-700/30 p-6 rounded-lg border border-gray-600">
              <h3 className="text-lg font-semibold text-gray-300 mb-2">Total Plays</h3>
              <p className="text-3xl font-bold text-white mb-2">{analytics.totalDownloads}</p>
              <span className="text-gray-500 text-sm">across all games</span>
            </div>

            <div className="bg-gray-700/30 p-6 rounded-lg border border-gray-600">
              <h3 className="text-lg font-semibold text-gray-300 mb-2">Ad Impressions</h3>
              <p className="text-3xl font-bold text-white mb-2">{analytics.totalAdImpressions}</p>
              <span className="text-gray-500 text-sm">estimated total</span>
            </div>

            <div className="bg-gray-700/30 p-6 rounded-lg border border-gray-600">
              <h3 className="text-lg font-semibold text-gray-300 mb-2">Ad Revenue</h3>
              <p className="text-3xl font-bold text-green-400 mb-2">${analytics.totalAdRevenue}</p>
              <span className="text-gray-500 text-sm">estimated earnings</span>
            </div>

            <div className="bg-gray-700/30 p-6 rounded-lg border border-gray-600">
              <h3 className="text-lg font-semibold text-gray-300 mb-2">Average Rating</h3>
              <p className="text-3xl font-bold text-white mb-2">{analytics.averageRating || '0.0'}</p>
              <span className="text-gray-500 text-sm">across all games</span>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default UploadsSection;
