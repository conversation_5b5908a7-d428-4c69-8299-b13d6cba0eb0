import { useState } from 'react';
import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';
import { FaGamepad } from 'react-icons/fa';
import GameCard from '../GameCard';

const LibrarySection = ({ games }) => {
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  
  // Filter games based on filter and search term
  const filteredGames = games.filter(game => {
    if (filter !== 'all' && game.genre !== filter) return false;
    if (searchTerm && !game.title.toLowerCase().includes(searchTerm.toLowerCase())) return false;
    return true;
  });
  
  return (
    <section className="space-y-6">
      <h1 className="text-3xl font-bold text-white">Game Library</h1>
      
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1">
          <input
            type="text"
            placeholder="Search games..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-colors duration-200"
          />
        </div>
        
        <div className="md:w-48">
          <select 
            value={filter} 
            onChange={(e) => setFilter(e.target.value)}
            className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-red-400 transition-colors duration-200"
          >
            <option value="all">All Genres</option>
            <option value="action">Action</option>
            <option value="adventure">Adventure</option>
            <option value="rpg">RPG</option>
            <option value="simulation">Simulation</option>
            <option value="strategy">Strategy</option>
            <option value="puzzle">Puzzle</option>
          </select>
        </div>
      </div>
      
      {filteredGames.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredGames.map(game => (
            <GameCard key={game.id} game={game} />
          ))}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center py-16 text-center">
          <FaGamepad className="text-6xl text-gray-600 mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">No games found</h3>
          <p className="text-gray-400 mb-6">
            {searchTerm || filter !== 'all' ? 
              'Try changing your search or filter' : 
              'Your game library is empty'}
          </p>
          <Link 
            to="/browse" 
            className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 no-underline"
          >
            Browse Games
          </Link>
        </div>
      )}
    </section>
  );
};

LibrarySection.propTypes = {
  games: PropTypes.array.isRequired
};

export default LibrarySection;
