import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';
import { FaHeart } from 'react-icons/fa';
import GameCard from '../GameCard';

const WishlistSection = ({ wishlist }) => {
  return (
    <section className="space-y-6">
      <h1 className="text-3xl font-bold text-white">Wishlist</h1>
      
      {wishlist.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {wishlist.map(game => (
            <div key={game.id} className="space-y-4">
              <GameCard game={game} />
              <div className="flex gap-2">
                <button className="flex-1 bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                  Remove
                </button>
                <Link 
                  to={`/game/${game.id}`} 
                  className="flex-1 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 text-center no-underline"
                >
                  {game.paymentType === 'free' ? 'Get Free' : 'Buy Now'}
                </Link>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center py-16 text-center">
          <FaHeart className="text-6xl text-gray-600 mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">Your wishlist is empty</h3>
          <p className="text-gray-400 mb-6">Save games you&apos;re interested in to your wishlist</p>
          <Link 
            to="/browse" 
            className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 no-underline"
          >
            Browse Games
          </Link>
        </div>
      )}
    </section>
  );
};

WishlistSection.propTypes = {
  wishlist: PropTypes.array.isRequired
};

export default WishlistSection;
