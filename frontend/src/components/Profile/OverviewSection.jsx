import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';
import { FaGamepad, FaCreditCard, FaShoppingCart } from 'react-icons/fa';
import GameCard from '../GameCard';

const OverviewSection = ({ user, games, credits, setActiveSection, createLanguageLink }) => {
  const recentGames = games.slice(0, 3);
  
  return (
    <section className="space-y-8">
      <h1 className="text-3xl font-bold text-white mb-6">Welcome, {user.username}!</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-700/50 p-6 rounded-lg border border-gray-600 hover:border-gray-500 transition-colors duration-200">
          <FaGamepad className="text-3xl text-red-400 mb-4" />
          <div>
            <h3 className="text-lg font-semibold text-gray-200 mb-2">Game Library</h3>
            <p className="text-3xl font-bold text-white mb-1">{games.length}</p>
            <p className="text-gray-400 text-sm">games in collection</p>
          </div>
        </div>
        
        <div className="bg-gray-700/50 p-6 rounded-lg border border-gray-600 hover:border-gray-500 transition-colors duration-200">
          <FaCreditCard className="text-3xl text-yellow-400 mb-4" />
          <div>
            <h3 className="text-lg font-semibold text-gray-200 mb-2">Credits Balance</h3>
            <p className="text-3xl font-bold text-white mb-1">{credits}</p>
            <p className="text-gray-400 text-sm">available credits</p>
          </div>
        </div>
      </div>
      
      {recentGames.length > 0 && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold text-white">Recently Played</h2>
            <button 
              onClick={() => setActiveSection('library')} 
              className="text-red-400 hover:text-red-300 font-medium transition-colors duration-200"
            >
              View All
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {recentGames.map(game => (
              <GameCard key={game.id} game={game} />
            ))}
          </div>
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Link
          to={createLanguageLink('/browse')}
          className="bg-gray-700/50 p-6 rounded-lg border border-gray-600 hover:border-red-400 transition-all duration-200 hover:-translate-y-1 no-underline text-gray-200 hover:text-white"
        >
          <FaGamepad className="text-3xl text-red-400 mb-4" />
          <div>
            <h3 className="text-lg font-semibold mb-2">Browse Games</h3>
            <p className="text-gray-400 text-sm">Find new games to play</p>
          </div>
        </Link>
        
        <Link
          to={createLanguageLink('/upload-game')}
          className="bg-gray-700/50 p-6 rounded-lg border border-gray-600 hover:border-red-400 transition-all duration-200 hover:-translate-y-1 no-underline text-gray-200 hover:text-white"
        >
          <FaShoppingCart className="text-3xl text-red-400 mb-4" />
          <div>
            <h3 className="text-lg font-semibold mb-2">Upload Game</h3>
            <p className="text-gray-400 text-sm">Share your game with others</p>
          </div>
        </Link>

        <Link
          to={createLanguageLink('/buy-credits')}
          className="bg-gray-700/50 p-6 rounded-lg border border-gray-600 hover:border-red-400 transition-all duration-200 hover:-translate-y-1 no-underline text-gray-200 hover:text-white"
        >
          <FaCreditCard className="text-3xl text-yellow-400 mb-4" />
          <div>
            <h3 className="text-lg font-semibold mb-2">Buy Credits</h3>
            <p className="text-gray-400 text-sm">Add credits to your account</p>
          </div>
        </Link>
      </div>
    </section>
  );
};

OverviewSection.propTypes = {
  user: PropTypes.object.isRequired,
  games: PropTypes.array.isRequired,
  credits: PropTypes.number.isRequired,
  setActiveSection: PropTypes.func.isRequired,
  createLanguageLink: PropTypes.func.isRequired
};

export default OverviewSection;
