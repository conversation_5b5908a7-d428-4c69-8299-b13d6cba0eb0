import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';
import { FaGamepad, FaHeart } from 'react-icons/fa';
import GameCard from '../GameCard';
import { getGameCategories } from '../../utils/categoryUtils';

const OverviewSection = ({ user, games, setActiveSection, createLanguageLink }) => {
  const recentGames = games.slice(0, 3);

  // Calculate favorite genre based on play history (mock implementation)
  const getFavoriteGenre = () => {
    if (games.length === 0) return null;

    // Count genres from games (this would be based on actual play history in real app)
    const genreCounts = {};
    games.forEach(game => {
      const genre = game.genre || 'action';
      genreCounts[genre] = (genreCounts[genre] || 0) + 1;
    });

    // Find most common genre
    const favoriteGenreValue = Object.keys(genreCounts).reduce((a, b) =>
      genreCounts[a] > genreCounts[b] ? a : b
    );

    // Get the proper label from categories
    const categories = getGameCategories();
    const category = categories.find(cat => cat.value === favoriteGenreValue);
    return category ? category.label : favoriteGenreValue;
  };

  const favoriteGenre = getFavoriteGenre();

  return (
    <section className="space-y-8">
      <h1 className="text-3xl font-bold text-white mb-6">Welcome, {user.username}!</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-700/50 p-6 rounded-lg border border-gray-600 hover:border-gray-500 transition-colors duration-200">
          <FaGamepad className="text-3xl text-red-400 mb-4" />
          <div>
            <h3 className="text-lg font-semibold text-gray-200 mb-2">Games Played</h3>
            <p className="text-3xl font-bold text-white mb-1">{games.length}</p>
            <p className="text-gray-400 text-sm">games in your history</p>
          </div>
        </div>

        <div className="bg-gray-700/50 p-6 rounded-lg border border-gray-600 hover:border-gray-500 transition-colors duration-200">
          <FaHeart className="text-3xl text-red-400 mb-4" />
          <div>
            <h3 className="text-lg font-semibold text-gray-200 mb-2">Favorite Genre</h3>
            {favoriteGenre ? (
              <Link
                to={createLanguageLink(`/?search=${favoriteGenre.toLowerCase()}`)}
                className="text-3xl font-bold text-white mb-1 hover:text-red-400 transition-colors duration-200 no-underline"
              >
                {favoriteGenre}
              </Link>
            ) : (
              <p className="text-3xl font-bold text-white mb-1">-</p>
            )}
            <p className="text-gray-400 text-sm">based on play history</p>
          </div>
        </div>
      </div>
      
      {recentGames.length > 0 && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold text-white">Recently Played</h2>
            <button 
              onClick={() => setActiveSection('library')} 
              className="text-red-400 hover:text-red-300 font-medium transition-colors duration-200"
            >
              View All
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {recentGames.map(game => (
              <GameCard key={game.id} game={game} />
            ))}
          </div>
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Link
          to={createLanguageLink('/upload-game')}
          className="bg-gray-700/50 p-6 rounded-lg border border-gray-600 hover:border-red-400 transition-all duration-200 hover:-translate-y-1 no-underline text-gray-200 hover:text-white"
        >
          <FaGamepad className="text-3xl text-red-400 mb-4" />
          <div>
            <h3 className="text-lg font-semibold mb-2">Upload Game</h3>
            <p className="text-gray-400 text-sm">Share your game with others</p>
          </div>
        </Link>

        <button
          onClick={() => setActiveSection('favorites')}
          className="bg-gray-700/50 p-6 rounded-lg border border-gray-600 hover:border-red-400 transition-all duration-200 hover:-translate-y-1 text-gray-200 hover:text-white text-left"
        >
          <FaHeart className="text-3xl text-red-400 mb-4" />
          <div>
            <h3 className="text-lg font-semibold mb-2">View Favorites</h3>
            <p className="text-gray-400 text-sm">See your favorite games</p>
          </div>
        </button>
      </div>
    </section>
  );
};

OverviewSection.propTypes = {
  user: PropTypes.object.isRequired,
  games: PropTypes.array.isRequired,
  setActiveSection: PropTypes.func.isRequired,
  createLanguageLink: PropTypes.func.isRequired
};

export default OverviewSection;
